import json
from dataclasses import asdict
from logging import getLogger
from typing import List, Optional, cast, Any, AsyncIterator, AsyncGenerator
from uuid import uuid4

from agno.app.playground.operator import (
    get_session_title,
    get_session_title_from_workflow_session,
    get_session_title_from_team_session,
)
from agno.app.playground.schemas import (
    AgentSessionsResponse,
    AgentRenameRequest,
)
from fastapi import (
    APIRouter,
    Path,
    Depends,
    File,
    Form,
    UploadFile,
    Body,
    HTTPException,
)
from fastapi.responses import StreamingResponse

from ai.common.base_common import (
    create_entity_instance,
    run_entity_with_files,
    EntityInfo,
    get_entity_instance,
)
from ai.registry import agents_map
from app.core.core_auth_deps import get_current_active_user
from app.models.auth import User

logger = getLogger(__name__)

WORKFLOW_HEARTBEAT_INTERVAL = 5  # Workflow心跳间隔（秒）

router = APIRouter(prefix="/agents", tags=["Agents"])


@router.get("/status")
async def playground_status(app_id: Optional[str] = None):
    """Playground状态检查接口"""
    return {"playground": "available"}


@router.get("", response_model=List[EntityInfo])
async def list_entities():
    """获取所有可用实体的详细信息列表（Agent、Team、Workflow）"""
    entity_infos = []

    for entity_id, entity_factory in agents_map.items():
        temp_instance, entity_type = get_entity_instance(entity_factory)
        entity_infos.append(
            EntityInfo(
                entity_id=entity_id,
                name=temp_instance.name or entity_id,
                description=temp_instance.description or f"{entity_type} instance",
                entity_type=entity_type,
            )
        )

    return entity_infos


@router.post("/{entity_id}/runs")
async def run_entity(
    entity_id: str = Path(..., description="实体ID"),
    message: str = Form(..., description="用户消息"),
    stream: bool = Form(True, description="是否流式响应"),
    monitor: bool = Form(False, description="是否启用监控"),
    session_id: Optional[str] = Form(None, description="会话ID"),
    model: Optional[str] = Form("gpt-4.1", description="使用的模型"),
    files: Optional[List[UploadFile]] = File(None, description="上传的文件"),
    current_user: User = Depends(get_current_active_user),
):
    user_id = str(current_user.id)
    if not session_id:
        session_id = str(uuid4())
    entity_instance = create_entity_instance(entity_id, user_id, session_id)

    if hasattr(entity_instance, "workflow_id"):
        # Workflow处理 - 手动初始化后使用arun方法
        entity_instance.user_id = user_id
        entity_instance.session_name = None

        # 手动初始化workflow
        entity_instance.run_id = str(uuid4())
        if entity_instance.session_id is None:

            entity_instance.session_id = str(uuid4())

        async def workflow_generator():
            # 使用 arun_workflow 而不是 arun
            result = await entity_instance.arun_workflow(topic=message)

            if isinstance(result, (AsyncIterator, AsyncGenerator)):
                async for item in result:
                    if hasattr(item, "__dataclass_fields__"):
                        yield json.dumps(asdict(cast(Any, item))) + "\n"
                    else:
                        yield json.dumps({"content": str(item)}) + "\n"
            else:
                yield json.dumps(asdict(result)) + "\n"

        return StreamingResponse(
            workflow_generator(),
            media_type="text/event-stream",
        )
    else:
        # Agent和Team使用通用处理
        return await run_entity_with_files(
            entity=entity_instance,
            message=message,
            monitor=monitor,
            stream=stream,
            files=files,
        )


@router.get("/{entity_id}/sessions", response_model=List[AgentSessionsResponse])
async def get_sessions(
    entity_id: str = Path(..., description="Agent ID"),
    current_user: User = Depends(get_current_active_user),
):
    """获取Agent会话列表"""
    entity = create_entity_instance(entity_id, str(current_user.id))
    sessions = entity.storage.get_all_sessions(
        user_id=str(current_user.id), entity_id=entity_id
    )
    session_entries = []
    for session in sessions:
        if hasattr(entity, "agent_id"):
            title = get_session_title(session)
        elif hasattr(entity, "team_id"):
            title = get_session_title_from_team_session(session)
        else:
            title = get_session_title_from_workflow_session(session)
        session_entries.append(
            AgentSessionsResponse(
                session_id=session.session_id,
                title=title,
                created_at=session.created_at,
            )
        )
    return session_entries


@router.get("/{entity_id}/sessions/{session_id}")
async def get_session(
    entity_id: str = Path(..., description="Agent ID"),
    session_id: str = Path(..., description="会话ID"),
    current_user: User = Depends(get_current_active_user),
):
    """获取Agent会话详情"""
    entity = create_entity_instance(entity_id, str(current_user.id))

    # 使用agno官方方式 - 直接返回session对象
    session = entity.storage.read(session_id, str(current_user.id))
    if session is None:
        raise HTTPException(status_code=404, detail="会话不存在")

    # 直接返回session对象，不做自定义数据处理
    return session


@router.post("/{entity_id}/sessions/{session_id}/rename")
async def rename_session(
    entity_id: str = Path(..., description="Agent ID"),
    session_id: str = Path(..., description="会话ID"),
    rename_request: AgentRenameRequest = Body(..., description="重命名请求"),
    current_user: User = Depends(get_current_active_user),
):
    """重命名Agent会话"""
    entity = create_entity_instance(entity_id, str(current_user.id))

    success = entity.storage.rename_session(
        session_id, rename_request.name, str(current_user.id)
    )
    if not success:
        raise HTTPException(status_code=404, detail="会话不存在或重命名失败")
    return {"message": "会话重命名成功"}


@router.delete("/{entity_id}/sessions/{session_id}")
async def delete_session(
    entity_id: str = Path(..., description="Agent ID"),
    session_id: str = Path(..., description="会话ID"),
    current_user: User = Depends(get_current_active_user),
):
    """删除Agent会话"""
    """删除Agent会话"""
    entity = create_entity_instance(entity_id, str(current_user.id))

    success = entity.storage.delete_session(session_id, str(current_user.id))
    if not success:
        raise HTTPException(status_code=404, detail="会话不存在或删除失败")
    return {"message": "会话删除成功"}
